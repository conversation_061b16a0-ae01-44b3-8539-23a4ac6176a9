{"name": "aplet360-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@cloudinary/react": "^1.13.1", "@cloudinary/url-gen": "^1.21.0", "@hookform/resolvers": "^3.9.1", "axios": "^1.7.7", "clsx": "^2.1.1", "date-fns": "^4.1.0", "date-fns-timezone": "^0.1.4", "flutterwave-react-v3": "^1.3.2", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "numeral": "^2.0.6", "react": "^18.3.1", "react-datepicker": "^7.6.0", "react-dom": "^18.3.1", "react-hook-form": "^7.53.1", "react-hot-toast": "^2.4.1", "react-icons": "^5.4.0", "react-paystack": "^6.0.0", "react-phone-number-input": "^3.4.12", "react-router-dom": "^7.1.1", "swiper": "^11.2.0", "yup": "^1.4.0", "zustand": "^5.0.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@iconify/react": "^5.0.2", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "vite": "^6.0.5"}}